# N-Sigma 算法在资源监控中的实现总结

## 概述

本次实现在 `servers/daemons/fusa/src/resource_monitor.c` 中集成了 n-sigma 算法，用于动态阈值检测，以减少传统固定阈值监控的误报问题。

## 实现的监控接口分析

### 适合 n-sigma 算法的接口

#### 1. CPU 相关监控
- **`sys_stats_get_cpu_timeslice()`** - CPU 时间片使用率 ✅ **已实现**
- **`sys_stats_get_proc_timeslice()`** - 进程 CPU 时间片 ✅ **已实现**
- **`sys_stats_get_thread_timeslice()`** - 线程 CPU 时间片 ✅ **已实现**

**适用原因**: CPU 使用率是连续数值，具有明显的统计特征，适合 n-sigma 异常检测。

#### 2. 内存相关监控
- **`sys_stats_get_sys_mem()`** - 系统内存使用率 ✅ **已实现**
- **`sys_stats_get_proc_mem()`** - 进程内存使用 ✅ **已实现**
- **`sys_stats_get_psv_mem()`** - PSV 内存使用 ✅ **已实现**

**适用原因**: 内存使用量是连续变化的数值，可以建立统计模型进行异常检测。

#### 3. 文件系统相关监控
- **`fs_stats_memory_get()`** - 文件系统内存使用 ✅ **已实现**
- **`fs_stats_io_latency_get_by_shm()`** - IO 延迟统计 ✅ **已实现**
- **`fs_stats_io_twb_get()`** - IO 写放大统计 ✅ **已实现**

**适用原因**: IO 延迟和文件系统内存使用都是连续数值，具有统计意义。

#### 4. 中断监控
- **`sys_stats_get_irq_stats()`** - 中断统计信息 ✅ **已实现**

**适用原因**: 中断频率可以作为连续数值进行统计分析。

### 不适合 n-sigma 算法的接口

#### 1. 状态类接口
- **`thread_is_active/proc_is_active`** - 布尔状态 ❌
- **`sys_stats_get_thread_state`** - 线程状态枚举 ❌

**原因**: 这些是离散的状态值，不是连续数值，无法进行统计分析。

#### 2. 配置类接口
- **`sys_stats_get_thread_affinity/proc_affinity`** - CPU 亲和性 ❌
- **`sys_stats_get_thread_prio/proc_prio`** - 优先级 ❌

**原因**: 这些是配置参数，不是性能指标，变化不频繁且无统计意义。

## 技术实现详情

### 1. N-Sigma 算法核心结构

```c
typedef struct {
    double samples[NSIGMA_WINDOW_SIZE];     // 循环缓冲区，存储历史样本
    int head;                               // 当前位置指针
    int count;                              // 已收集的样本数量
    double sigma;                           // Sigma 阈值（默认 3.0）
    double mean;                            // 当前均值
    double std_dev;                         // 当前标准差
    bool enabled;                           // 是否启用 n-sigma 检测
} nsigma_detector_t;
```

### 2. 关键算法函数

- **`nsigma_init()`**: 初始化检测器
- **`nsigma_add_sample()`**: 添加新样本并更新统计信息
- **`nsigma_calculate_stats()`**: 计算均值和标准差
- **`nsigma_is_anomaly()`**: 判断是否为异常值

### 3. 异常检测逻辑

```c
double z_score = fabs(value - detector->mean) / detector->std_dev;
return z_score > detector->sigma;
```

当 Z-score 超过设定的 sigma 值时，判定为异常。

## 配置参数

- **`NSIGMA_WINDOW_SIZE`**: 50 - 历史样本窗口大小
- **`NSIGMA_DEFAULT_SIGMA`**: 3.0 - 默认 sigma 阈值
- **`NSIGMA_MIN_SAMPLES`**: 10 - 开始检测前的最小样本数

## 新增的监控功能

### 1. 扩展的 CPU 监控
- 保持原有的固定阈值检测
- 新增 n-sigma 动态异常检测
- 支持多核 CPU 监控

### 2. 扩展的内存监控
- 系统内存使用率监控
- 进程内存使用监控
- 文件系统内存监控

### 3. 新增的 IO 监控
- IO 延迟异常检测
- 文件系统性能监控

### 4. 新增的中断监控
- 中断频率异常检测
- 系统负载分析

## API 接口

### 配置接口
```c
// 启用/禁用特定监控类型的 n-sigma 检测
int fusa_nsigma_set_enabled(monitor_type_t type, bool enabled);

// 设置特定监控类型的 sigma 阈值
int fusa_nsigma_set_sigma(monitor_type_t type, double sigma);

// 获取当前统计信息
int fusa_nsigma_get_stats(monitor_type_t type, double *mean, double *std_dev, int *sample_count);
```

### 监控类型
```c
typedef enum {
    MONITOR_CPU_USAGE = 0,    // CPU 使用率
    MONITOR_MEM_USAGE,        // 内存使用率
    MONITOR_PROC_MEM,         // 进程内存
    MONITOR_IO_LATENCY,       // IO 延迟
    MONITOR_IRQ_COUNT,        // 中断计数
    MONITOR_FS_MEMORY,        // 文件系统内存
    MONITOR_TYPE_MAX
} monitor_type_t;
```

## 优势

1. **减少误报**: 动态阈值适应系统负载变化
2. **自适应**: 算法会根据历史数据自动调整检测基线
3. **统一接口**: 所有监控类型使用相同的 n-sigma 算法框架
4. **可配置**: 支持运行时调整 sigma 值和启用/禁用检测
5. **兼容性**: 保持原有固定阈值检测，新增 n-sigma 作为补充

## 测试验证

通过 `test_nsigma.c` 验证了算法的正确性：
- 正常数据不会触发异常检测
- 异常数据能够被正确识别
- 算法具有自适应能力，能够适应数据变化

## 使用建议

1. **初期运行**: 建议先收集足够的基线数据（至少 50 个样本）
2. **Sigma 调整**: 根据实际需求调整 sigma 值（3.0 为标准值）
3. **监控组合**: 结合传统阈值和 n-sigma 检测，提高检测准确性
4. **定期评估**: 定期检查检测效果，必要时调整参数

## 文件修改清单

- `servers/daemons/fusa/src/resource_monitor.c` - 主要实现文件
- `servers/daemons/fusa/include/resource_monitor.h` - 头文件更新
- `servers/daemons/fusa/CMakeLists.txt` - 添加数学库链接
