#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <errno.h>
#include <pthread.h>
#include <sys/shm.h>
#include <sel4/sel4.h>
#include <core/syscalls.h>
#include <core/fusa.h>
#include <safe_test.h>

#ifdef FUSA_SAFETEST

/**
 * @brief
 * - Memory write: Write specific values to specific memory address
 * @param
 * - Memory address
 * - Memory length
 * @return
 * - None
 */
static void memory_write_test(void *memory_block, size_t size_in_bytes)
{
    for (size_t i = 0; i < size_in_bytes; i += MEM_TEST_STRIDE) {
        *((char *)memory_block + i) = (char)(i % 256);
    }

    *((char *)memory_block + size_in_bytes - 1) = (char)((size_in_bytes - 1) % 256);
}

/**
 * @brief
 * - Memory verify: Verify memory data at specific memory address
 * @param
 * - Memory address
 * - Memory length
 * @return
 * - Verify result
 */
static int memory_read_test(void *memory_block, size_t size_in_bytes)
{
    for (size_t i = 0; i < size_in_bytes; i += MEM_TEST_STRIDE) {
        if (*((char *)memory_block + i) != (char)(i % 256)) {
            printf("Data verification failed at position %zu. Expected: %u, Actual: %u\n", i, (char)(i % 256), *((char *)memory_block + i));
            return ST_FAILED;
        }
    }

    // Test lat byte of memory area
    if (*((char *)memory_block + size_in_bytes - 1) != (char)((size_in_bytes - 1) % 512)) {
        printf("Data verification failed at lat byte. Expected: %u, Actual: %u\n", (char)(size_in_bytes - 1 % 256),
               *((char *)memory_block + size_in_bytes - 1));
        return ST_FAILED;
    }

    return ST_OK;
}

/**
 * @brief
 * - Memory APIs test: malloc() memory then write and verify memory area
 * @param
 * - Memory test length
 * @return
 * - Memory APIs test result
 */
static int memory_test(size_t size_in_bytes)
{
    void *memory_block = malloc(size_in_bytes);

    if (memory_block == NULL) {
        if (errno == ENOMEM)
            return ST_OK;

        printf("Memory allocation %ld bytes failed: %s\n", size_in_bytes, strerror(errno));
        return ST_FAILED;
    }

    memory_write_test(memory_block, size_in_bytes);

    if (memory_read_test(memory_block, size_in_bytes) != ST_OK) {
        printf("Memory test %ld bytes failed\n", size_in_bytes);
        free(memory_block);
        return ST_FAILED;
    }

    free(memory_block);
    return ST_OK;
}

/**
 * @brief
 * - PSv memory safe test: 4K and 2M memory length are tested
 * @param
 * - None
 * @return
 * - Safe test result
 */
static int mem_safe_test(void)
{
    int ret = ST_OK;

    //4K page test
    ret = memory_test(PAGE_4K_SIZE);
    if (ret != ST_OK) {
        return ST_FAILED;
    }

    //2M page test
    ret = memory_test(PAGE_2M_SIZE);
    if (ret != ST_OK) {
        return ST_FAILED;
    }

    return ST_OK;
}

#ifdef FUSA_SAFETEST_FILE_OPS
/**
 * @brief
 * - FSv file operations APIs safet test: open(), read(), write(), close, lseek()
 * @param
 * - File test path
 * @return
 * - File APIs test result
 */
static int fsv_file_ops_safetest(const char *file_path, bool *bootup_phase)
{
    // Create and open file
    char buffer[ST_FILE_TEST_SIZE] = {0};
    int fd = -1;
    int ret = ST_OK;

    memory_write_test(buffer, ST_FILE_TEST_SIZE);

    fd = open(file_path, O_RDWR | O_CREAT);
    if (fd < 0) {
        //If it is bootup and FS is read-only, that means fs check or fsck, ignore it
        if ((*bootup_phase) && (errno == EROFS)) return ST_OK;

        printf("Error open %s file: %s\n", file_path, strerror(errno));
        return ST_FAILED;
    }

    *bootup_phase = false;

    // Write data
    ssize_t bytes_written = write(fd, buffer, sizeof(buffer));
    if (bytes_written == -1) {
        printf("Error write to file %s: %s\n", file_path, strerror(errno));
        close(fd);

        return ST_FAILED;
    }

    if (lseek(fd, 0, SEEK_SET) == -1) {
        printf("Error seek to the beginning of the file %s: %s\n", file_path, strerror(errno));
        close(fd);

        return ST_FAILED;
    }

    // Read data
    memset(buffer, 0, sizeof(buffer));
    ssize_t bytes_read = read(fd, buffer, sizeof(buffer));
    if (bytes_read == -1) {
        printf("Error read from file %s: %s\n", file_path, strerror(errno));
        close(fd);

        return ST_FAILED;
    }

    // Verify data
    ret = memory_read_test(buffer, ST_FILE_TEST_SIZE);
    if (ret != ST_OK) {
        printf("Error verify file %s\n", file_path);
    }

    //Close file
    close(fd);

    return ret;
}
#endif //FUSA_SAFETEST_FILE_OPS

#ifdef FUSA_SAFETEST_SHM_API
/**
 * @brief
 * - Share memory safetest test thread handler: Get shm and write date
 * @param
 * - argv
 * @return
 * - None
 */
void *shm_safetest_thread(void UNUSED *unused_args)
{
    int shmid = -1;
    char *shared_mem = NULL;

    // Get the share memory
    shmid = shmget(SAFETEST_SHM_KEY, SAFETEST_SHM_SIZE, 0666);
    if (shmid == -1) {
        printf("shmget failed in thread: %s\n", strerror(errno));
        pthread_exit(NULL);
    }

    //Attach shared memory to the process address space
    shared_mem = shmat(shmid, NULL, 0);
    if (shared_mem == (char *) -1) {
        printf("shmat failed in thread: %s\n", strerror(errno));
        pthread_exit(NULL);
    }

    // Write data into share memory
    memory_write_test(shared_mem, SAFETEST_SHM_SIZE);

    //Detach share memory
    shmdt(shared_mem);

    return NULL;
}

/**
 * @brief
 * - Share memory APIs test: Create share memory and verify memory area after test thread write the shm
 * @param
 * - None
 * @return
 * - SHM APIs test result
 */
int psv_shm_safetest(void)
{
    int shmid = -1;
    char *shared_mem = NULL;
    int ret = 0;

    // Create shared memory
    shmid = shmget(SAFETEST_SHM_KEY, SAFETEST_SHM_SIZE, IPC_CREAT | 0666);
    if (shmid == -1) {
        printf("Error creating shared memory: %s\n", strerror(errno));
        return ST_FAILED;
    }

    // Attach shared memory to the process address space
    shared_mem = shmat(shmid, NULL, 0);
    if (shared_mem == (char *)(-1)) {
        printf("shmat failed: %s\n", strerror(errno));
        return ST_FAILED;
    }

    // Create test thread
    pthread_t thread;
    if (pthread_create(&thread, NULL, shm_safetest_thread, NULL)) {
        printf("Error creating thread: %s\n", strerror(errno));
        return ST_FAILED;
    }

    // Wait for the test process to finish
    if (pthread_join(thread, NULL)) {
        printf("Error joining thread: %s\n", strerror(errno));
        return ST_FAILED;
    }

    ret = memory_read_test(shared_mem, SAFETEST_SHM_SIZE);

    //Detach share memory
    shmdt(shared_mem);

    return ret;
}
#endif //FUSA_SAFETEST_SHM_API

/**
 * @brief
 * - PSv related APIs safe test
 * @param
 * - None
 * @return
 * - PSv APIs safe test result
 */
int fusa_psv_safetest(void)
{
    int ret = ST_OK;

    ret = mem_safe_test();
    if (ret != ST_OK) {
        printf("Memory operations API Test Failed!\n");

        return ST_FAILED;
    }

#ifdef FUSA_SAFETEST_SHM_API
    ret = psv_shm_safetest();
    if (ret != ST_OK) {
        printf("Shared memory Test Failed!\n");

        return ST_FAILED;
    }
#endif

    return ST_OK;
}

#ifdef FUSA_SAFETEST_FILE_OPS
/**
 * @brief
 * - FSv related APIs safe test
 * @param
 * - None
 * @return
 * - FSv APIs safe test result
 */
int fusa_fsv_safetest(void)
{
    int ret = ST_OK;
    static bool blk9_bootup_phase = true;
    static bool ramd_bootup_phase = true;

    ret = fsv_file_ops_safetest(ST_FS_BLK9_TEST_PATH, &blk9_bootup_phase);
    if (ret != ST_OK)
        return ret;

    ret = fsv_file_ops_safetest(ST_FS_RAMD_TEST_PATH, &ramd_bootup_phase);
    if (ret != ST_OK)
        return ret;

    return ret;
}
#endif

/**
 * @brief
 * - Safe test handler
 * @param
 * - None
 * @return
 * - None
 */
void fusa_safetest_handler(void)
{
    uint32_t err_code = 0;
    int ret = 0;

    ret = fusa_psv_safetest();
    if (ret != ST_OK) {
        err_code = ERR_ST_PSV;
        sys_fusa_safehub_err_rpt(err_code);
    }

#ifdef FUSA_SAFETEST_FILE_OPS
    ret = fusa_fsv_safetest();
    if (ret != ST_OK) {
        err_code = ERR_ST_FSV;
        sys_fusa_safehub_err_rpt(err_code);
    }
#endif
}

int main(int UNUSED argc, char UNUSED **argv)
{
    printf("Safetest app starts\n");

    // allow 20s for servers to boot up
    sleep(20);
    while (1) {
        fusa_safetest_handler();
        sleep(SAFETEST_CYCLE_SECOND);
    }

    return 0;
}
#else     // FUSA_SAFETEST
int main(int UNUSED argc, char UNUSED **argv)
{
    /* Do nothing */
    printf("Safe test APP: Do nothing!!\n");
    return 0;
}
#endif    // FUSA_SAFETEST
