#pragma once
#include <safetest/gen_config.h>

#define FUSA_SAFETEST_LEVEL_NONE           0
#define FUSA_SAFETEST_LEVEL_BASIC          1
#define FUSA_SAFETEST_LEVEL_MAIN           2
#define FUSA_SAFETEST_LEVEL_ALL            3

#define FUSA_SAFETEST_LEVEL CONFIG_FUSA_SAFETEST_LEVEL

//Baic level only inlcudes memory API safe test in PSv
#if FUSA_SAFETEST_LEVEL >= FUSA_SAFETEST_LEVEL_BASIC
#define FUSA_SAFETEST
#endif

//All level: Safe test for all APIs in current code
#if FUSA_SAFETEST_LEVEL >= FUSA_SAFETEST_LEVEL_ALL
#define FUSA_SAFETEST_SHM_API
#endif

#define ST_OK                             (0)
#define ST_FAILED                         (1)

//Safe test frequency
#define SAFETEST_CYCLE_SECOND             (1)

#define MEM_TEST_STRIDE                   (256)

//Memory Test
#define PAGE_4K_SIZE                      0x1000
#define PAGE_2M_SIZE                      0x200000

#ifdef FUSA_SAFETEST_FILE_OPS
//File operations test
#define ST_FS_BLK9_TEST_PATH              "/var/fusa_fs_blk9_test"
#define ST_FS_RAMD_TEST_PATH              "/dev/shm/fusa_fs_ramdisk_test"
#define ST_FILE_TEST_SIZE                 (1024)
#endif

//Share memory operations test
#define SAFETEST_SHM_KEY                  (0x73616665)  //The ASCII code of "safe"
#define SAFETEST_SHM_SIZE                 (1024)
