#define ZF_LOG_LEVEL CONFIG_FUSA_LOG_LEVEL
#include <fusa/gen_config.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <sys/stat.h>
#include <sysstats/stats.h>
#include <fs_stats.h>
#include <math.h>

#include <safehub.h>
#include <resource_monitor.h>

// N-sigma algorithm configuration
#define NSIGMA_WINDOW_SIZE          50      // Number of samples to keep
#define NSIGMA_DEFAULT_SIGMA        3.0     // Default sigma value
#define NSIGMA_MIN_SAMPLES          10      // Minimum samples before detection

// N-sigma algorithm data structure
typedef struct {
    double samples[NSIGMA_WINDOW_SIZE];     // Circular buffer for samples
    int head;                               // Current position in buffer
    int count;                              // Number of samples collected
    double sigma;                           // Sigma threshold value
    double mean;                            // Current mean
    double std_dev;                         // Current standard deviation
    bool enabled;                           // Whether n-sigma is enabled for this monitor
} nsigma_detector_t;

// Resource monitor data structure
typedef struct {
    monitor_type_t type;
    nsigma_detector_t detector;
    uint64_t last_value;                    // Last measured value
    char name[32];                          // Monitor name for logging
} resource_monitor_t;

static uint64_t idle_slice[CONFIG_MAX_NUM_NODES] = {0};
static uint64_t idle_old_slice[CONFIG_MAX_NUM_NODES] = {0};
static uint64_t core_slice[CONFIG_MAX_NUM_NODES] = {0};
static uint64_t core_old_slice[CONFIG_MAX_NUM_NODES] = {0};

// N-sigma monitors for different resource types
static resource_monitor_t monitors[MONITOR_TYPE_MAX];

typedef uint16_t percent_t; // real percentage * 100

// N-sigma algorithm functions
static void nsigma_init(nsigma_detector_t *detector, double sigma)
{
    memset(detector, 0, sizeof(nsigma_detector_t));
    detector->sigma = sigma;
    detector->enabled = true;
}

static void nsigma_calculate_stats(nsigma_detector_t *detector)
{
    if (detector->count < 2) {
        detector->mean = 0.0;
        detector->std_dev = 0.0;
        return;
    }

    // Calculate mean
    double sum = 0.0;
    int samples_to_use = (detector->count < NSIGMA_WINDOW_SIZE) ? detector->count : NSIGMA_WINDOW_SIZE;

    for (int i = 0; i < samples_to_use; i++) {
        sum += detector->samples[i];
    }
    detector->mean = sum / samples_to_use;

    // Calculate standard deviation
    double variance = 0.0;
    for (int i = 0; i < samples_to_use; i++) {
        double diff = detector->samples[i] - detector->mean;
        variance += diff * diff;
    }
    detector->std_dev = sqrt(variance / samples_to_use);
}

static void nsigma_add_sample(nsigma_detector_t *detector, double value)
{
    if (!detector->enabled) return;

    // Add sample to circular buffer
    detector->samples[detector->head] = value;
    detector->head = (detector->head + 1) % NSIGMA_WINDOW_SIZE;

    if (detector->count < NSIGMA_WINDOW_SIZE) {
        detector->count++;
    }

    // Recalculate statistics
    nsigma_calculate_stats(detector);
}

static bool nsigma_is_anomaly(nsigma_detector_t *detector, double value)
{
    if (!detector->enabled || detector->count < NSIGMA_MIN_SAMPLES) {
        return false;
    }

    if (detector->std_dev == 0.0) {
        return false; // No variation in data
    }

    double z_score = fabs(value - detector->mean) / detector->std_dev;
    return z_score > detector->sigma;
}

static void monitor_init(resource_monitor_t *monitor, monitor_type_t type, const char *name)
{
    monitor->type = type;
    monitor->last_value = 0;
    strncpy(monitor->name, name, sizeof(monitor->name) - 1);
    monitor->name[sizeof(monitor->name) - 1] = '\0';
    nsigma_init(&monitor->detector, NSIGMA_DEFAULT_SIGMA);
}

static percent_t get_core_load(int core)
{
    uint64_t idle_load =  idle_slice[core] - idle_old_slice[core];
    uint64_t core_load =  core_slice[core] - core_old_slice[core];

    if (core_load == 0)
        return 0;

    return (100 - 100 * idle_load / core_load);
}

/**
 * @brief Get the CPU usage of each core with n-sigma anomaly detection
 * @param[in] void
 * @return error code
 */
static int cpu_check_threshold(void)
{
    int err = 0;
    int ret_val = 0;
    percent_t cpu_load[CONFIG_MAX_NUM_NODES] = { 0 };

    for (int i = 0; i < CONFIG_MAX_NUM_NODES; i++) {
        err = sys_stats_get_cpu_timeslice(i, &core_slice[i]);
        if (err) {
            ret_val = err;
            continue;
        }

        err = sys_stats_get_thread_timeslice(0, PSV_IDLE_TID_START + i, &idle_slice[i]);
        if (err) {
            ret_val = err;
            continue;
        }

        // real cpu percentage * 100
        cpu_load[i] = get_core_load(i);

        // Add sample to n-sigma detector
        nsigma_add_sample(&monitors[MONITOR_CPU_USAGE].detector, (double)cpu_load[i]);

        // Check both traditional threshold and n-sigma anomaly
        bool traditional_anomaly = cpu_load[i] > SAFETY_CPU_THRESHOLD;
        bool nsigma_anomaly = nsigma_is_anomaly(&monitors[MONITOR_CPU_USAGE].detector, (double)cpu_load[i]);

        if (traditional_anomaly || nsigma_anomaly) {
            struct safety_notify_msg msg = {0};
            msg.code = (uint32_t)ERR_CPU_OT;
            msg.extend[0] = cpu_load[i];
            msg.extend[1] = i;
            msg.extend[2] = nsigma_anomaly ? 1 : 0; // Flag to indicate n-sigma detection

            safehub_error_report(msg);

            if (nsigma_anomaly) {
                ZF_LOGW("Fusa: Core %d CPU anomaly detected by n-sigma: %d%% (mean=%.2f, std=%.2f)",
                        i, cpu_load[i], monitors[MONITOR_CPU_USAGE].detector.mean,
                        monitors[MONITOR_CPU_USAGE].detector.std_dev);
            }
        }

        core_old_slice[i] = core_slice[i];
        idle_old_slice[i] = idle_slice[i];
        ZF_LOGV("Fusa: Core %d,  %d%%", i, cpu_load[i]);
    }

    return ret_val;
}

/**
 * @brief Get the system memory usage with n-sigma anomaly detection
 * @param[in] void
 * @return error code
 */
static int mem_check_threshold(void)
{
    int err = 0;
    struct sys_mem_stats sys_mem = { 0 };
    int mem_load = 0;

    err = sys_stats_get_sys_mem(&sys_mem);
    if (err) return err;

    ZF_LOGV("Fusa: total  %ld MB, avail %ld MB, free %ld MB, used %ld MB, cached %ld MB\n", sys_mem.total_usable / (1024 * 1024),
            sys_mem.available / (1024 * 1024), sys_mem.remaining / (1024 * 1024),
            (sys_mem.total_usable - sys_mem.available) / (1024 * 1024),
            (sys_mem.n_cached_4k * BIT(PAGE_BITS_4K) + sys_mem.n_cached_2m * BIT(PAGE_BITS_2M)) / (1024 * 1024));

    if (sys_mem.total_usable == 0) return FUSA_ERROR;

    mem_load = (sys_mem.total_usable - sys_mem.available) * 100 / sys_mem.total_usable;
    ZF_LOGV("Fusa: memory usage is %d%%\n", mem_load);

    // Add sample to n-sigma detector
    nsigma_add_sample(&monitors[MONITOR_MEM_USAGE].detector, (double)mem_load);

    // Check both traditional threshold and n-sigma anomaly
    bool traditional_anomaly = mem_load > SAFETY_MEM_THRESHOLD;
    bool nsigma_anomaly = nsigma_is_anomaly(&monitors[MONITOR_MEM_USAGE].detector, (double)mem_load);

    if (traditional_anomaly || nsigma_anomaly) {
        struct safety_notify_msg msg = {0};
        msg.code = (uint32_t)ERR_MEM_OT;
        msg.extend[0] = mem_load;
        msg.extend[1] = nsigma_anomaly ? 1 : 0; // Flag to indicate n-sigma detection

        safehub_error_report(msg);

        if (nsigma_anomaly) {
            ZF_LOGW("Fusa: Memory anomaly detected by n-sigma: %d%% (mean=%.2f, std=%.2f)",
                    mem_load, monitors[MONITOR_MEM_USAGE].detector.mean,
                    monitors[MONITOR_MEM_USAGE].detector.std_dev);
        }
    }

    return err;
}

/**
 * @brief Monitor process memory usage with n-sigma anomaly detection
 * @param[in] void
 * @return error code
 */
static int proc_mem_check_threshold(void)
{
    int err = 0;
    size_t total_proc_mem = 0;
    int active_procs = 0;

    // Iterate through all processes and sum their memory usage
    for (pid_t pid = 0; pid < CONFIG_PROC_MAX_NUM_PROC; pid++) {
        const struct proc_stats *proc = sys_stats_get_proc_stats(pid);
        if (proc && proc->active) {
            size_t proc_mem = 0;
            err = sys_stats_get_proc_mem(pid, &proc_mem);
            if (err == 0) {
                total_proc_mem += proc_mem;
                active_procs++;
            }
        }
    }

    if (active_procs == 0) return 0;

    // Convert to MB for easier handling
    double proc_mem_mb = (double)total_proc_mem / (1024 * 1024);

    // Add sample to n-sigma detector
    nsigma_add_sample(&monitors[MONITOR_PROC_MEM].detector, proc_mem_mb);

    // Check for anomaly (no traditional threshold for process memory)
    bool nsigma_anomaly = nsigma_is_anomaly(&monitors[MONITOR_PROC_MEM].detector, proc_mem_mb);

    if (nsigma_anomaly) {
        ZF_LOGW("Fusa: Process memory anomaly detected by n-sigma: %.2f MB (mean=%.2f, std=%.2f)",
                proc_mem_mb, monitors[MONITOR_PROC_MEM].detector.mean,
                monitors[MONITOR_PROC_MEM].detector.std_dev);
    }

    ZF_LOGV("Fusa: Total process memory: %.2f MB (%d active processes)", proc_mem_mb, active_procs);
    return 0;
}

/**
 * @brief Monitor interrupt statistics with n-sigma anomaly detection
 * @param[in] void
 * @return error code
 */
static int irq_check_threshold(void)
{
    int err = 0;
    uint16_t irq_count = 0;
    uint32_t *irq_stats = NULL;

    err = sys_stats_get_irq_stats(&irq_count, &irq_stats);
    if (err || !irq_stats) return err;

    // Calculate total interrupt count
    uint64_t total_irq = 0;
    for (int i = 0; i < irq_count; i++) {
        total_irq += irq_stats[i];
    }

    // Calculate interrupt rate (difference from last measurement)
    static uint64_t last_total_irq = 0;
    uint64_t irq_rate = (total_irq > last_total_irq) ? (total_irq - last_total_irq) : 0;
    last_total_irq = total_irq;

    // Add sample to n-sigma detector
    nsigma_add_sample(&monitors[MONITOR_IRQ_COUNT].detector, (double)irq_rate);

    // Check for anomaly (no traditional threshold for IRQ rate)
    bool nsigma_anomaly = nsigma_is_anomaly(&monitors[MONITOR_IRQ_COUNT].detector, (double)irq_rate);

    if (nsigma_anomaly) {
        ZF_LOGW("Fusa: IRQ rate anomaly detected by n-sigma: %lu (mean=%.2f, std=%.2f)",
                irq_rate, monitors[MONITOR_IRQ_COUNT].detector.mean,
                monitors[MONITOR_IRQ_COUNT].detector.std_dev);
    }

    ZF_LOGV("Fusa: IRQ rate: %lu, total: %lu", irq_rate, total_irq);
    return 0;
}

/**
 * @brief Monitor filesystem memory usage with n-sigma anomaly detection
 * @param[in] void
 * @return error code
 */
static int fs_mem_check_threshold(void)
{
    int err = 0;
    struct fs_memory_stats fs_mem = { 0 };

    err = fs_stats_memory_get(EXTFS_SVC_NAME, &fs_mem);
    if (err) return err;

    // Calculate total filesystem memory usage in MB
    double fs_mem_mb = (double)fs_mem.vma_size / (1024 * 1024);

    // Add sample to n-sigma detector
    nsigma_add_sample(&monitors[MONITOR_FS_MEMORY].detector, fs_mem_mb);

    // Check for anomaly (no traditional threshold for FS memory)
    bool nsigma_anomaly = nsigma_is_anomaly(&monitors[MONITOR_FS_MEMORY].detector, fs_mem_mb);

    if (nsigma_anomaly) {
        ZF_LOGW("Fusa: FS memory anomaly detected by n-sigma: %.2f MB (mean=%.2f, std=%.2f)",
                fs_mem_mb, monitors[MONITOR_FS_MEMORY].detector.mean,
                monitors[MONITOR_FS_MEMORY].detector.std_dev);
    }

    ZF_LOGV("Fusa: FS memory usage: %.2f MB", fs_mem_mb);
    return 0;
}

int fusa_resource_monitor_init(void)
{
    int err = 0;

    err = sys_stats_init();
    if (err) return err;

    // Initialize all monitors
    monitor_init(&monitors[MONITOR_CPU_USAGE], MONITOR_CPU_USAGE, "CPU Usage");
    monitor_init(&monitors[MONITOR_MEM_USAGE], MONITOR_MEM_USAGE, "Memory Usage");
    monitor_init(&monitors[MONITOR_PROC_MEM], MONITOR_PROC_MEM, "Process Memory");
    monitor_init(&monitors[MONITOR_IO_LATENCY], MONITOR_IO_LATENCY, "IO Latency");
    monitor_init(&monitors[MONITOR_IRQ_COUNT], MONITOR_IRQ_COUNT, "IRQ Count");
    monitor_init(&monitors[MONITOR_FS_MEMORY], MONITOR_FS_MEMORY, "FS Memory");

    ZF_LOGI("Fusa: Resource monitor initialized with n-sigma anomaly detection");
    return err;
}

int fusa_resource_monitor_func(void)
{
    int err = 0;

    // Core monitoring functions (CPU and Memory)
    err = mem_check_threshold();
    if (err) ZF_LOGE("Fusa: memory monitor failed (err=%d)", err);

    err = cpu_check_threshold();
    if (err) ZF_LOGE("Fusa: cpu monitor failed (err=%d)", err);

    // Extended monitoring functions with n-sigma
    err = proc_mem_check_threshold();
    if (err) ZF_LOGE("Fusa: process memory monitor failed (err=%d)", err);

    err = irq_check_threshold();
    if (err) ZF_LOGE("Fusa: IRQ monitor failed (err=%d)", err);

    err = fs_mem_check_threshold();
    if (err) ZF_LOGE("Fusa: filesystem memory monitor failed (err=%d)", err);

    return err;
}

// Additional utility functions for n-sigma configuration

/**
 * @brief Enable/disable n-sigma detection for a specific monitor type
 * @param[in] type Monitor type
 * @param[in] enabled Enable/disable flag
 * @return error code
 */
int fusa_nsigma_set_enabled(monitor_type_t type, bool enabled)
{
    if (type >= MONITOR_TYPE_MAX) return EINVAL;

    monitors[type].detector.enabled = enabled;
    ZF_LOGI("Fusa: N-sigma detection %s for %s",
            enabled ? "enabled" : "disabled", monitors[type].name);
    return 0;
}

/**
 * @brief Set sigma threshold for a specific monitor type
 * @param[in] type Monitor type
 * @param[in] sigma Sigma threshold value
 * @return error code
 */
int fusa_nsigma_set_sigma(monitor_type_t type, double sigma)
{
    if (type >= MONITOR_TYPE_MAX) return EINVAL;
    if (sigma <= 0.0) return EINVAL;

    monitors[type].detector.sigma = sigma;
    ZF_LOGI("Fusa: Sigma threshold set to %.2f for %s", sigma, monitors[type].name);
    return 0;
}

/**
 * @brief Get current statistics for a monitor
 * @param[in] type Monitor type
 * @param[out] mean Current mean value
 * @param[out] std_dev Current standard deviation
 * @param[out] sample_count Number of samples collected
 * @return error code
 */
int fusa_nsigma_get_stats(monitor_type_t type, double *mean, double *std_dev, int *sample_count)
{
    if (type >= MONITOR_TYPE_MAX) return EINVAL;
    if (!mean || !std_dev || !sample_count) return EINVAL;

    nsigma_detector_t *detector = &monitors[type].detector;
    *mean = detector->mean;
    *std_dev = detector->std_dev;
    *sample_count = detector->count;

    return 0;
}
