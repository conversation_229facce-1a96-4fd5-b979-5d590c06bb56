#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include <stdbool.h>
#include <string.h>

// Copy the n-sigma algorithm structures and functions for testing
#define NSIGMA_WINDOW_SIZE          50
#define NSIGMA_DEFAULT_SIGMA        3.0
#define NSIGMA_MIN_SAMPLES          10

typedef struct {
    double samples[NSIGMA_WINDOW_SIZE];
    int head;
    int count;
    double sigma;
    double mean;
    double std_dev;
    bool enabled;
} nsigma_detector_t;

static void nsigma_init(nsigma_detector_t *detector, double sigma)
{
    memset(detector, 0, sizeof(nsigma_detector_t));
    detector->sigma = sigma;
    detector->enabled = true;
}

static void nsigma_calculate_stats(nsigma_detector_t *detector)
{
    if (detector->count < 2) {
        detector->mean = 0.0;
        detector->std_dev = 0.0;
        return;
    }

    // Calculate mean
    double sum = 0.0;
    int samples_to_use = (detector->count < NSIGMA_WINDOW_SIZE) ? detector->count : NSIGMA_WINDOW_SIZE;
    
    for (int i = 0; i < samples_to_use; i++) {
        sum += detector->samples[i];
    }
    detector->mean = sum / samples_to_use;

    // Calculate standard deviation
    double variance = 0.0;
    for (int i = 0; i < samples_to_use; i++) {
        double diff = detector->samples[i] - detector->mean;
        variance += diff * diff;
    }
    detector->std_dev = sqrt(variance / samples_to_use);
}

static void nsigma_add_sample(nsigma_detector_t *detector, double value)
{
    if (!detector->enabled) return;

    // Add sample to circular buffer
    detector->samples[detector->head] = value;
    detector->head = (detector->head + 1) % NSIGMA_WINDOW_SIZE;
    
    if (detector->count < NSIGMA_WINDOW_SIZE) {
        detector->count++;
    }

    // Recalculate statistics
    nsigma_calculate_stats(detector);
}

static bool nsigma_is_anomaly(nsigma_detector_t *detector, double value)
{
    if (!detector->enabled || detector->count < NSIGMA_MIN_SAMPLES) {
        return false;
    }

    if (detector->std_dev == 0.0) {
        return false; // No variation in data
    }

    double z_score = fabs(value - detector->mean) / detector->std_dev;
    return z_score > detector->sigma;
}

// Test function
void test_nsigma_algorithm()
{
    nsigma_detector_t detector;
    nsigma_init(&detector, 3.0);
    
    printf("Testing N-Sigma Algorithm\n");
    printf("=========================\n\n");
    
    // Test 1: Normal data (should not trigger anomalies)
    printf("Test 1: Adding normal data (50-60 range)\n");
    for (int i = 0; i < 20; i++) {
        double value = 50.0 + (rand() % 10); // Values between 50-60
        nsigma_add_sample(&detector, value);
        bool anomaly = nsigma_is_anomaly(&detector, value);
        
        if (detector.count >= NSIGMA_MIN_SAMPLES) {
            printf("Sample %2d: %.1f, Mean: %.2f, StdDev: %.2f, Anomaly: %s\n", 
                   i+1, value, detector.mean, detector.std_dev, anomaly ? "YES" : "NO");
        } else {
            printf("Sample %2d: %.1f (building baseline)\n", i+1, value);
        }
    }
    
    printf("\n");
    
    // Test 2: Add anomalous data (should trigger anomalies)
    printf("Test 2: Adding anomalous data\n");
    double anomalous_values[] = {90.0, 95.0, 10.0, 5.0, 100.0};
    int num_anomalous = sizeof(anomalous_values) / sizeof(anomalous_values[0]);
    
    for (int i = 0; i < num_anomalous; i++) {
        double value = anomalous_values[i];
        bool anomaly = nsigma_is_anomaly(&detector, value);
        nsigma_add_sample(&detector, value);
        
        printf("Anomalous %d: %.1f, Mean: %.2f, StdDev: %.2f, Anomaly: %s\n", 
               i+1, value, detector.mean, detector.std_dev, anomaly ? "YES" : "NO");
    }
    
    printf("\n");
    
    // Test 3: Return to normal (should not trigger anomalies after adaptation)
    printf("Test 3: Return to normal data\n");
    for (int i = 0; i < 10; i++) {
        double value = 50.0 + (rand() % 10); // Values between 50-60
        bool anomaly = nsigma_is_anomaly(&detector, value);
        nsigma_add_sample(&detector, value);
        
        printf("Normal %2d: %.1f, Mean: %.2f, StdDev: %.2f, Anomaly: %s\n", 
               i+1, value, detector.mean, detector.std_dev, anomaly ? "YES" : "NO");
    }
    
    printf("\nTest completed!\n");
}

int main()
{
    test_nsigma_algorithm();
    return 0;
}
